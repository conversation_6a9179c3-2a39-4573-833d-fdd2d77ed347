import React from 'react';
import { getSiteSettings } from '@/services/siteSettingsService';
import HomePageClient from '@/components/HomePageClient';

// Export metadata directly in the page file
export const metadata = {
  title: 'Mocky Digital - Web Design & Graphic Design Services Nairobi Kenya',
  description: 'Leading creative agency in Nairobi offering professional web development, graphic design, and digital marketing services. Affordable website designers and graphic designers in Nairobi Kenya.',
  keywords: 'web development services nairobi, graphic design services nairobi, website designers nairobi, affordable graphic designers nairobi, logo design nairobi, creative agency nairobi, web design agency nairobi, branding services nairobi, digital marketing nairobi, professional websites nairobi, custom website development nairobi, business card design nairobi, social media design nairobi, nairobi design studio, freelance graphic designer nairobi'
};

// Server Component
export default async function Home() {
  // Get site settings
  const settings = await getSiteSettings();

  return (
    <main className="overflow-hidden">
      <HomePageClient settings={settings} />
    </main>
  );
}
