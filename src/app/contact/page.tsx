import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - Get Your Free Quote | Mocky Digital Kenya',
  description: 'Ready to transform your digital presence? Contact Mocky Digital today for a free consultation. Web design, graphic design, and digital marketing services in Kenya.',
  keywords: 'contact mocky digital, free quote, digital agency kenya, web design contact, logo design contact, nairobi, consultation, project inquiry',
  openGraph: {
    title: 'Contact Mocky Digital - Get Your Free Quote Today',
    description: 'Ready to transform your digital presence? Contact Kenya\'s leading digital agency for a free consultation on web design, graphic design, and digital marketing.',
    type: 'website',
    url: 'https://mocky.co.ke/contact',
    siteName: 'Mocky Digital',
    locale: 'en_KE',
    images: [
      {
        url: '/images/contact-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Contact Mocky Digital - Get Your Free Quote',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Mocky Digital - Get Your Free Quote Today',
    description: 'Ready to transform your digital presence? Contact Kenya\'s leading digital agency for a free consultation.',
    images: ['/images/contact-og.jpg'],
    creator: '@mockydigital',
    site: '@mockydigital',
  },
  alternates: {
    canonical: '/contact',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function Contact() {
  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20 mt-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-1 bg-[#FF5400]"></div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Get in Touch</h1>
            <p className="text-lg md:text-xl text-gray-300 mb-4">Let's create something amazing together</p>
          </div>
        </div>
      </section>

      {/* Contact Cards */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6 mx-auto">
                <i className="fas fa-map-marker-alt text-2xl text-primary"></i>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-2">Visit Us</h3>
                <p className="text-gray-600">Kahawa Wendani<br/>Thika Road, Nairobi</p>
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6 mx-auto">
                <i className="fas fa-envelope text-2xl text-primary"></i>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-2">Email Us</h3>
                <p className="text-gray-600"><EMAIL></p>
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6 mx-auto">
                <i className="fas fa-phone text-2xl text-primary"></i>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-2">Call Us</h3>
                <p className="text-gray-600">+254 741 590 670</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form and Map Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
            {/* Form Side */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h2 className="text-3xl font-bold mb-8 text-gradient">Send Us a Message</h2>

              <div className="space-y-6">
                <form action="/api/contact" method="POST" className="space-y-6" id="contactForm">
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                      placeholder="How can we help you?"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        Your Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                        placeholder="John Doe"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Your Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                        placeholder="+254 700 000000"
                      />
                    </div>

                    <div>
                      <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-1">
                        Service Interested In
                      </label>
                      <select
                        id="service"
                        name="service"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                      >
                        <option value="">Select a Service</option>
                        <option value="web-design">Web Design</option>
                        <option value="logo-design">Logo Design</option>
                        <option value="graphic-design">Graphic Design</option>
                        <option value="seo">SEO & Marketing</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      Your Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                      placeholder="Tell us about your project..."
                      required
                    ></textarea>
                  </div>

                  <div id="formStatus" className="hidden"></div>

                  <button
                    type="submit"
                    id="submitBtn"
                    className="w-full py-3 px-6 bg-primary text-white font-medium rounded-lg hover:bg-primary-dark transition-colors"
                  >
                    Send Message
                  </button>
                </form>
              </div>

              {/* Client-side form handling script */}
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                    document.addEventListener('DOMContentLoaded', function() {
                      const form = document.getElementById('contactForm');
                      const statusDiv = document.getElementById('formStatus');
                      const submitBtn = document.getElementById('submitBtn');

                      if (form) {
                        form.addEventListener('submit', async function(e) {
                          e.preventDefault();

                          // Show loading state
                          submitBtn.disabled = true;
                          submitBtn.innerText = 'Sending...';
                          statusDiv.className = 'hidden';

                          try {
                            const formData = new FormData(form);
                            const response = await fetch('/api/contact', {
                              method: 'POST',
                              body: formData,
                            });

                            const result = await response.json();

                            // Display result
                            statusDiv.classList.remove('hidden');

                            if (response.ok) {
                              statusDiv.className = 'p-4 bg-green-50 text-green-700 rounded-lg mb-4';
                              statusDiv.innerText = result.message;
                              form.reset();
                            } else {
                              statusDiv.className = 'p-4 bg-red-50 text-red-700 rounded-lg mb-4';
                              statusDiv.innerText = result.message || 'Something went wrong. Please try again.';
                            }
                          } catch (error) {
                            console.error('Error submitting form:', error);
                            statusDiv.classList.remove('hidden');
                            statusDiv.className = 'p-4 bg-red-50 text-red-700 rounded-lg mb-4';
                            statusDiv.innerText = 'There was a problem submitting your form. Please try again later.';
                          } finally {
                            // Reset button
                            submitBtn.disabled = false;
                            submitBtn.innerText = 'Send Message';
                          }
                        });
                      }
                    });
                  `
                }}
              />
            </div>

            {/* Map Side */}
            <div className="rounded-xl overflow-hidden shadow-lg">
              <div className="h-[600px] w-full">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d15955.156511195512!2d36.93501535!3d-1.1897397!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f3f59c85e45ad%3A0x9e18d7940617a214!2sKahawa%20Wendani%2C%20Nairobi!5e0!3m2!1sen!2ske!4v1709913427349!5m2!1sen!2ske"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Mocky Digital Kenya Location"
                  aria-label="Google Maps showing Mocky Digital Kenya location in Kahawa Wendani, Nairobi"
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}