import { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  tags?: string[];
  noIndex?: boolean;
}

/**
 * Generate comprehensive metadata for Next.js 15 App Router
 * Optimized for social media sharing and SEO
 */
export function generateMetadata(config: SEOConfig): Metadata {
  const baseUrl = 'https://mocky.co.ke';
  const fullUrl = config.url ? `${baseUrl}${config.url}` : baseUrl;
  const ogImage = config.image || '/images/default-og.jpg';
  
  return {
    title: config.title,
    description: config.description,
    keywords: config.keywords,
    
    // Open Graph
    openGraph: {
      title: config.title,
      description: config.description,
      type: config.type || 'website',
      url: fullUrl,
      siteName: 'Mocky Digital',
      locale: 'en_KE',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: config.title,
        },
      ],
      ...(config.type === 'article' && {
        publishedTime: config.publishedTime,
        modifiedTime: config.modifiedTime,
        authors: config.authors,
        tags: config.tags,
      }),
    },
    
    // Twitter
    twitter: {
      card: 'summary_large_image',
      title: config.title,
      description: config.description,
      images: [ogImage],
      creator: '@mockydigital',
      site: '@mockydigital',
    },
    
    // Canonical URL
    alternates: {
      canonical: config.url || '/',
    },
    
    // Robots
    robots: {
      index: !config.noIndex,
      follow: true,
      googleBot: {
        index: !config.noIndex,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

/**
 * Nairobi-focused keyword sets for different services
 */
export const nairobiKeywords = {
  graphicDesign: [
    'graphic design services nairobi',
    'affordable graphic designers nairobi',
    'logo design nairobi',
    'business card design nairobi',
    'poster design services nairobi',
    'brochure design nairobi',
    'social media design nairobi',
    'creative agency nairobi',
    'freelance graphic designer nairobi',
    'branding services nairobi',
    'custom design nairobi kenya',
    'digital marketing design nairobi',
    'professional designers in nairobi',
    'nairobi design studio',
    'brand identity nairobi'
  ],

  webDevelopment: [
    'web development services nairobi',
    'website designers nairobi',
    'affordable website developers nairobi',
    'custom website development nairobi',
    'web design agency nairobi',
    'e-commerce website nairobi',
    'responsive web design nairobi',
    'nairobi website developers',
    'wordpress development nairobi',
    'website redesign nairobi',
    'seo web development nairobi',
    'freelance web developer nairobi',
    'business website nairobi kenya',
    'professional websites nairobi',
    'web hosting and design nairobi'
  ],

  combined: [
    'graphic and web design nairobi',
    'branding and web development nairobi',
    'creative agency nairobi kenya',
    'digital solutions nairobi',
    'marketing and design agency nairobi',
    'affordable design and development nairobi',
    'logo and website design nairobi',
    'all-in-one design services nairobi'
  ]
};

/**
 * Default metadata configurations for common pages
 */
export const defaultSEOConfigs = {
  home: {
    title: 'Mocky Digital - Web Design & Graphic Design Services Nairobi Kenya',
    description: 'Leading creative agency in Nairobi offering professional web development, graphic design, and digital marketing services. Affordable website designers and graphic designers in Nairobi Kenya.',
    keywords: [
      ...nairobiKeywords.combined,
      ...nairobiKeywords.graphicDesign.slice(0, 5),
      ...nairobiKeywords.webDevelopment.slice(0, 5),
      'digital marketing agency nairobi',
      'professional branding nairobi',
      'seo services nairobi'
    ].join(', '),
    image: '/images/default-og.jpg',
  },

  about: {
    title: 'About Mocky Digital - Leading Creative Agency Nairobi Kenya',
    description: 'Learn about Mocky Digital, Nairobi\'s premier creative agency. Our mission, vision, values, and the expert team behind innovative web design and graphic design solutions in Kenya.',
    keywords: [
      'about mocky digital',
      'creative agency nairobi kenya',
      'web design company nairobi',
      'graphic design team nairobi',
      'digital marketing experts nairobi',
      'professional designers in nairobi',
      'nairobi design studio'
    ].join(', '),
    image: '/images/about/team-og.jpg',
    url: '/about',
  },

  blog: {
    title: 'Design & Marketing Blog - Mocky Digital Nairobi',
    description: 'Expert insights on web design, graphic design, digital marketing, and business growth from Nairobi\'s leading creative agency. Tips and trends for Kenya businesses.',
    keywords: [
      'design blog nairobi',
      'digital marketing blog kenya',
      'web design tips nairobi',
      'graphic design trends kenya',
      'seo tips nairobi',
      'business growth kenya',
      'creative agency insights nairobi'
    ].join(', '),
    image: '/images/blog-default-og.jpg',
    url: '/blog',
  },

  contact: {
    title: 'Contact Mocky Digital - Web Design & Graphic Design Nairobi',
    description: 'Ready to transform your business? Contact Nairobi\'s leading creative agency for web development, graphic design, and digital marketing services. Get your free quote today.',
    keywords: [
      'contact mocky digital nairobi',
      'free quote web design nairobi',
      'graphic design consultation nairobi',
      'creative agency nairobi kenya',
      'web development services nairobi',
      'digital marketing consultation kenya'
    ].join(', '),
    image: '/images/contact-og.jpg',
    url: '/contact',
  },

  portfolio: {
    title: 'Portfolio - Web Design & Graphic Design Work Nairobi Kenya',
    description: 'Explore our portfolio of successful web design, graphic design, and digital marketing projects in Nairobi. See how we\'ve helped Kenya businesses grow with creative solutions.',
    keywords: [
      'design portfolio nairobi',
      'web design examples kenya',
      'graphic design portfolio nairobi',
      'digital marketing case studies kenya',
      'creative work nairobi',
      'professional websites nairobi',
      'branding projects kenya'
    ].join(', '),
    image: '/images/portfolio-og.jpg',
    url: '/portfolio',
  },

  graphics: {
    title: 'Graphic Design Services Nairobi - Logo, Branding & Print Design',
    description: 'Professional graphic design services in Nairobi. Affordable logo design, business cards, posters, brochures, and social media design. Creative agency serving all of Kenya.',
    keywords: nairobiKeywords.graphicDesign.join(', '),
    image: '/images/graphics-og.jpg',
    url: '/graphics',
  },

  webDevelopment: {
    title: 'Web Development Services Nairobi - Professional Website Design',
    description: 'Expert web development services in Nairobi. Custom websites, e-commerce, WordPress development, and responsive design. Affordable website developers in Kenya.',
    keywords: nairobiKeywords.webDevelopment.join(', '),
    image: '/images/web-development-og.jpg',
    url: '/web-development',
  },
};

/**
 * Generate service-specific metadata with Nairobi keywords
 */
export function generateServiceMetadata(service: 'graphics' | 'webDevelopment' | 'combined', customConfig?: Partial<SEOConfig>): Metadata {
  const baseConfig = service === 'graphics'
    ? defaultSEOConfigs.graphics
    : service === 'webDevelopment'
    ? defaultSEOConfigs.webDevelopment
    : defaultSEOConfigs.home;

  return generateMetadata({
    ...baseConfig,
    ...customConfig,
  });
}

/**
 * Generate location-specific landing page metadata
 */
export function generateLocationMetadata(location: string, service: string, customConfig?: Partial<SEOConfig>): Metadata {
  const locationKeywords = location.toLowerCase() === 'nairobi' ? nairobiKeywords : [];
  const serviceKeywords = service === 'graphic-design'
    ? locationKeywords.graphicDesign || []
    : service === 'web-development'
    ? locationKeywords.webDevelopment || []
    : locationKeywords.combined || [];

  return generateMetadata({
    title: `${service.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Services ${location} | Mocky Digital`,
    description: `Professional ${service.replace('-', ' ')} services in ${location}. Expert team delivering quality solutions for businesses across Kenya.`,
    keywords: serviceKeywords.slice(0, 10).join(', '),
    image: `/images/${service}-${location.toLowerCase()}-og.jpg`,
    url: `/${service}/${location.toLowerCase()}`,
    ...customConfig,
  });
}

/**
 * Generate blog post metadata with location keywords
 */
export function generateBlogMetadata(post: {
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  publishedAt?: string;
  updatedAt?: string;
  author?: string;
  tags?: string[];
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  location?: string;
}): Metadata {
  const locationKeywords = post.location?.toLowerCase() === 'nairobi'
    ? ['nairobi', 'kenya', 'east africa']
    : [];

  const allKeywords = [
    ...(post.seoKeywords || post.tags || []),
    ...locationKeywords
  ];

  return generateMetadata({
    title: post.seoTitle || `${post.title} | Mocky Digital Blog`,
    description: post.seoDescription || post.excerpt,
    keywords: allKeywords.join(', '),
    image: post.featuredImage || '/images/blog-default-og.jpg',
    url: `/blog/${post.slug}`,
    type: 'article',
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt,
    authors: post.author ? [post.author] : ['Mocky Digital'],
    tags: post.tags,
  });
}

/**
 * Validate Open Graph image requirements
 */
export function validateOGImage(imageUrl: string): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check if it's a relative URL (should be absolute for OG)
  if (imageUrl.startsWith('/')) {
    recommendations.push('Consider using absolute URLs for better social media compatibility');
  }
  
  // Check file extension
  const validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
  const hasValidExtension = validExtensions.some(ext => imageUrl.toLowerCase().includes(ext));
  
  if (!hasValidExtension) {
    issues.push('Image should be JPG, PNG, or WebP format');
  }
  
  // Recommendations for optimal OG images
  recommendations.push(
    'Ensure image is exactly 1200x630 pixels',
    'Keep file size under 1MB for faster loading',
    'Include brand elements and readable text',
    'Test with Facebook Sharing Debugger and Twitter Card Validator'
  );
  
  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}

/**
 * Generate structured data for articles
 */
export function generateArticleStructuredData(post: {
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  publishedAt?: string;
  updatedAt?: string;
  author?: string;
}) {
  const baseUrl = 'https://mocky.co.ke';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: post.excerpt,
    image: post.featuredImage ? `${baseUrl}${post.featuredImage}` : `${baseUrl}/images/blog-default-og.jpg`,
    url: `${baseUrl}/blog/${post.slug}`,
    datePublished: post.publishedAt,
    dateModified: post.updatedAt || post.publishedAt,
    author: {
      '@type': 'Person',
      name: post.author || 'Mocky Digital',
    },
    publisher: {
      '@type': 'Organization',
      name: 'Mocky Digital',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`,
      },
    },
  };
}
