import { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  tags?: string[];
  noIndex?: boolean;
}

/**
 * Generate comprehensive metadata for Next.js 15 App Router
 * Optimized for social media sharing and SEO
 */
export function generateMetadata(config: SEOConfig): Metadata {
  const baseUrl = 'https://mocky.co.ke';
  const fullUrl = config.url ? `${baseUrl}${config.url}` : baseUrl;
  const ogImage = config.image || '/images/default-og.jpg';
  
  return {
    title: config.title,
    description: config.description,
    keywords: config.keywords,
    
    // Open Graph
    openGraph: {
      title: config.title,
      description: config.description,
      type: config.type || 'website',
      url: fullUrl,
      siteName: 'Mocky Digital',
      locale: 'en_KE',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: config.title,
        },
      ],
      ...(config.type === 'article' && {
        publishedTime: config.publishedTime,
        modifiedTime: config.modifiedTime,
        authors: config.authors,
        tags: config.tags,
      }),
    },
    
    // Twitter
    twitter: {
      card: 'summary_large_image',
      title: config.title,
      description: config.description,
      images: [ogImage],
      creator: '@mockydigital',
      site: '@mockydigital',
    },
    
    // Canonical URL
    alternates: {
      canonical: config.url || '/',
    },
    
    // Robots
    robots: {
      index: !config.noIndex,
      follow: true,
      googleBot: {
        index: !config.noIndex,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

/**
 * Default metadata configurations for common pages
 */
export const defaultSEOConfigs = {
  home: {
    title: 'Mocky Digital - Professional Web Design & Digital Marketing Agency Kenya',
    description: 'Leading digital agency in Nairobi offering professional web design, graphic design, and digital marketing services. Transform your business with our expert team.',
    keywords: 'web design kenya, graphic design nairobi, digital marketing agency, seo services, professional branding, website development, social media marketing',
    image: '/images/default-og.jpg',
  },
  
  about: {
    title: 'About Us - Leading Digital Agency in Kenya | Mocky Digital',
    description: 'Learn about Mocky Digital, Kenya\'s premier digital agency. Our mission, vision, values, and the expert team behind innovative digital solutions.',
    keywords: 'about mocky digital, digital agency kenya, web design company nairobi, graphic design team, digital marketing experts',
    image: '/images/about/team-og.jpg',
    url: '/about',
  },
  
  blog: {
    title: 'Blog - Design & Marketing Insights | Mocky Digital',
    description: 'Expert insights on web design, graphic design, digital marketing, SEO, and business growth from Kenya\'s leading digital agency.',
    keywords: 'design blog, digital marketing blog, web design tips, graphic design trends, SEO tips, business growth',
    image: '/images/blog-default-og.jpg',
    url: '/blog',
  },
  
  contact: {
    title: 'Contact Us - Get Your Free Quote | Mocky Digital Kenya',
    description: 'Ready to transform your digital presence? Contact Mocky Digital today for a free consultation on web design, graphic design, and digital marketing.',
    keywords: 'contact mocky digital, free quote, digital agency kenya, consultation, project inquiry',
    image: '/images/contact-og.jpg',
    url: '/contact',
  },
  
  portfolio: {
    title: 'Portfolio - Our Best Work | Mocky Digital Kenya',
    description: 'Explore our portfolio of successful web design, graphic design, and digital marketing projects. See how we\'ve helped businesses grow in Kenya.',
    keywords: 'design portfolio, web design examples, graphic design portfolio, digital marketing case studies, kenya design work',
    image: '/images/portfolio-og.jpg',
    url: '/portfolio',
  },
};

/**
 * Generate blog post metadata
 */
export function generateBlogMetadata(post: {
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  publishedAt?: string;
  updatedAt?: string;
  author?: string;
  tags?: string[];
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}): Metadata {
  return generateMetadata({
    title: post.seoTitle || `${post.title} | Mocky Digital Blog`,
    description: post.seoDescription || post.excerpt,
    keywords: post.seoKeywords?.join(', ') || post.tags?.join(', '),
    image: post.featuredImage || '/images/blog-default-og.jpg',
    url: `/blog/${post.slug}`,
    type: 'article',
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt,
    authors: post.author ? [post.author] : ['Mocky Digital'],
    tags: post.tags,
  });
}

/**
 * Validate Open Graph image requirements
 */
export function validateOGImage(imageUrl: string): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check if it's a relative URL (should be absolute for OG)
  if (imageUrl.startsWith('/')) {
    recommendations.push('Consider using absolute URLs for better social media compatibility');
  }
  
  // Check file extension
  const validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
  const hasValidExtension = validExtensions.some(ext => imageUrl.toLowerCase().includes(ext));
  
  if (!hasValidExtension) {
    issues.push('Image should be JPG, PNG, or WebP format');
  }
  
  // Recommendations for optimal OG images
  recommendations.push(
    'Ensure image is exactly 1200x630 pixels',
    'Keep file size under 1MB for faster loading',
    'Include brand elements and readable text',
    'Test with Facebook Sharing Debugger and Twitter Card Validator'
  );
  
  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}

/**
 * Generate structured data for articles
 */
export function generateArticleStructuredData(post: {
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  publishedAt?: string;
  updatedAt?: string;
  author?: string;
}) {
  const baseUrl = 'https://mocky.co.ke';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: post.excerpt,
    image: post.featuredImage ? `${baseUrl}${post.featuredImage}` : `${baseUrl}/images/blog-default-og.jpg`,
    url: `${baseUrl}/blog/${post.slug}`,
    datePublished: post.publishedAt,
    dateModified: post.updatedAt || post.publishedAt,
    author: {
      '@type': 'Person',
      name: post.author || 'Mocky Digital',
    },
    publisher: {
      '@type': 'Organization',
      name: 'Mocky Digital',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`,
      },
    },
  };
}
