import { motion, useScroll, useTransform } from 'framer-motion';
import Image from 'next/image';
import { useRef, useEffect, useState } from 'react';
import MeetTheExpertsSlider from '@/components/AboutPage/MeetTheExpertsSlider';
import CompanyStory from '@/components/CompanyStory';
import Link from 'next/link';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us - Leading Digital Agency in Kenya | Mocky Digital',
  description: 'Learn about Mocky Digital, Kenya\'s premier digital agency. Our mission, vision, values, and the expert team behind innovative web design, graphic design, and digital marketing solutions.',
  keywords: 'about mocky digital, digital agency kenya, web design company nairobi, graphic design team, digital marketing experts, kenya design agency, professional web developers',
  openGraph: {
    title: 'About Mocky Digital - Leading Digital Agency in Kenya',
    description: 'Meet the team behind Kenya\'s premier digital agency. Discover our mission, vision, and commitment to delivering exceptional digital solutions.',
    type: 'website',
    url: 'https://mocky.co.ke/about',
    siteName: 'Mocky Digital',
    locale: 'en_KE',
    images: [
      {
        url: '/images/about/team-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Mocky Digital Team - Leading Digital Agency in Kenya',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Mocky Digital - Leading Digital Agency in Kenya',
    description: 'Meet the team behind Kenya\'s premier digital agency. Discover our mission, vision, and commitment to delivering exceptional digital solutions.',
    images: ['/images/about/team-og.jpg'],
    creator: '@mockydigital',
    site: '@mockydigital',
  },
  alternates: {
    canonical: '/about',
  },
  robots: {
    index: true,
    follow: true,
  },
};

const values = [
  {
    title: 'Innovation',
    icon: 'fas fa-lightbulb',
    description: 'Pushing boundaries with creative digital solutions',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Quality',
    icon: 'fas fa-circle-check',
    description: 'Delivering excellence in every project we undertake',
    color: 'from-[#0A2647] to-[#205295]'
  },
  {
    title: 'Integrity',
    icon: 'fas fa-heart',
    description: 'Building trust through honest business practices',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Collaboration',
    icon: 'fas fa-handshake',
    description: 'Working together to achieve exceptional results',
    color: 'from-[#0A2647] to-[#205295]'
  }
];

// Custom animated counter component
function Counter({ from, to, duration = 2 }) {
  const [count, setCount] = useState(from);

  useEffect(() => {
    let startTime;
    let animationFrame;

    const updateCount = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
      setCount(Math.floor(progress * (to - from) + from));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(updateCount);
      }
    };

    animationFrame = requestAnimationFrame(updateCount);
    return () => cancelAnimationFrame(animationFrame);
  }, [from, to, duration]);

  return <span>{count}+</span>;
}

'use client';

export default function About() {
  const targetRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(scrollYProgress, [0, 0.5], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 0.5], [50, 0]);

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <main className="pt-16 overflow-hidden">
      {/* Hero Section - Copied from Catalogue Page */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="about-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#about-grid)" />
          </svg>
        </div>

        {/* Enhanced Floating Elements - Responsive */}
        <div className="absolute top-10 sm:top-20 left-4 sm:left-10 w-20 sm:w-32 lg:w-40 h-20 sm:h-32 lg:h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-xl sm:blur-2xl animate-pulse"></div>
        <div className="absolute top-16 sm:top-32 right-4 sm:right-16 w-16 sm:w-24 lg:w-32 h-16 sm:h-24 lg:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-20 sm:bottom-40 left-1/4 w-14 sm:w-20 lg:w-28 h-14 sm:h-20 lg:h-28 bg-gradient-to-br from-green-200/25 to-teal-200/25 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-12 sm:bottom-24 right-1/4 sm:right-1/3 w-18 sm:w-28 lg:w-36 h-18 sm:h-28 lg:h-36 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl sm:blur-2xl animate-pulse" style={{ animationDelay: '0.8s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                ABOUT OUR COMPANY
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Meet{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Mocky
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Digital Agency
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              Discover our story, values, and the passionate team behind
              <span className="font-semibold text-gray-800"> innovative digital solutions that transform businesses</span>
            </p>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 sm:gap-6 justify-center px-4">
              <Link href="/contact" className="px-6 sm:px-8 py-3 rounded-full bg-[#FF5400] text-white font-medium hover:bg-[#cc4300] hover:shadow-lg hover:shadow-[#FF5400]/20 transition-all duration-300 transform hover:-translate-y-1">
                Get in Touch
              </Link>
              <Link href="/portfolio" className="px-6 sm:px-8 py-3 rounded-full bg-gray-100 border border-gray-200 text-gray-700 font-medium hover:bg-gray-200 transition-all duration-300">
                View Our Work
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Company Overview Section */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
                <i className="fas fa-users text-[#FF5400]"></i>
                <span className="text-sm font-medium text-[#FF5400]">Who We Are</span>
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                Crafting Digital <span className="text-[#FF5400]">Excellence</span>
              </h2>
            </motion.div>

            <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <p className="text-lg text-gray-600 leading-relaxed">
                  At Mocky Digital, we're more than just a design agency. We're your strategic partners in building a powerful digital presence that drives real business growth.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Since our founding, we've helped businesses of all sizes transform their ideas into compelling digital experiences. From startups to established enterprises, we bring the same level of creativity, technical expertise, and dedication to every project.
                </p>
                <div className="grid grid-cols-2 gap-6 pt-6">
                  <div className="text-center p-4 bg-gray-50 rounded-xl">
                    <div className="text-3xl font-bold text-[#FF5400] mb-2">50+</div>
                    <div className="text-sm text-gray-600">Projects Completed</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-xl">
                    <div className="text-3xl font-bold text-[#FF5400] mb-2">100%</div>
                    <div className="text-sm text-gray-600">Client Satisfaction</div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-[#FF5400]/10 to-[#0A2647]/10 p-8">
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-[#FF5400]/10 flex items-center justify-center flex-shrink-0">
                        <svg className="w-6 h-6 text-[#FF5400]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Innovation First</h3>
                        <p className="text-gray-600">We stay ahead of digital trends to deliver cutting-edge solutions.</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-[#0A2647]/10 flex items-center justify-center flex-shrink-0">
                        <svg className="w-6 h-6 text-[#0A2647]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Client-Centered</h3>
                        <p className="text-gray-600">Your success is our priority. We listen, understand, and deliver.</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-[#FF5400]/10 flex items-center justify-center flex-shrink-0">
                        <svg className="w-6 h-6 text-[#FF5400]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Results Driven</h3>
                        <p className="text-gray-600">Every design decision is backed by strategy and measurable outcomes.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
                <i className="fas fa-compass text-[#FF5400]"></i>
                <span className="text-sm font-medium text-[#FF5400]">Our Purpose</span>
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                Mission & <span className="text-[#FF5400]">Vision</span>
              </h2>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="relative p-8 bg-white rounded-2xl shadow-lg border border-gray-100"
              >
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-[#FF5400] to-[#ff7633] rounded-full flex items-center justify-center">
                    <i className="fas fa-bullseye text-white text-2xl"></i>
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To empower businesses with exceptional digital design and strategic marketing solutions that drive growth, enhance brand presence, and create meaningful connections with their audiences.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="relative p-8 bg-white rounded-2xl shadow-lg border border-gray-100"
              >
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-[#0A2647] to-[#205295] rounded-full flex items-center justify-center">
                    <i className="fas fa-eye text-white text-2xl"></i>
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To be the leading digital agency that transforms how businesses connect with their customers, setting new standards for creativity, innovation, and measurable results in the digital landscape.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <CompanyStory />

      {/* Core Values Section - Clean Design */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
                <i className="fas fa-star text-[#FF5400]"></i>
                <span className="text-sm font-medium text-[#FF5400]">Our Principles</span>
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                Core <span className="text-[#FF5400]">Values</span>
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                The foundation of our work ethic and the promises we make to our clients.
              </p>
            </motion.div>

            <div ref={targetRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -8, transition: { duration: 0.3 } }}
                  className="group relative p-6 lg:p-8 rounded-2xl bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {/* Gradient background on hover */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-[#FF5400]/5 to-[#0A2647]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Icon container */}
                  <div className={`relative mb-6 w-16 h-16 rounded-2xl bg-gradient-to-br ${value.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${value.icon} text-white text-2xl`}></i>
                  </div>

                  {/* Content */}
                  <div className="relative">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#0A2647] transition-colors duration-300">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {value.description}
                    </p>
                  </div>

                  {/* Decorative element */}
                  <div className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="w-full h-full rounded-full bg-gradient-to-br from-[#FF5400]/20 to-[#0A2647]/20"></div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Members Section */}
      <MeetTheExpertsSlider />

      {/* What Makes Us Different Section */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
                <i className="fas fa-trophy text-[#FF5400]"></i>
                <span className="text-sm font-medium text-[#FF5400]">Our Advantage</span>
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                What Makes Us <span className="text-[#FF5400]">Different</span>
              </h2>
              <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We don't just create beautiful designs—we build strategic solutions that deliver real business value.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
                className="group p-8 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 rounded-full bg-[#FF5400]/10 flex items-center justify-center mb-6 group-hover:bg-[#FF5400]/20 transition-colors duration-300">
                  <svg className="w-8 h-8 text-[#FF5400]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 tracking-tight">Design Excellence</h3>
                <p className="text-gray-600 leading-relaxed">
                  Every pixel matters. We create visually stunning designs that not only look great but also enhance user experience and drive conversions.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="group p-8 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 rounded-full bg-[#0A2647]/10 flex items-center justify-center mb-6 group-hover:bg-[#0A2647]/20 transition-colors duration-300">
                  <svg className="w-8 h-8 text-[#0A2647]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 tracking-tight">Strategic Approach</h3>
                <p className="text-gray-600 leading-relaxed">
                  We don't just execute—we strategize. Every project begins with understanding your goals and crafting a roadmap to achieve them.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="group p-8 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 rounded-full bg-[#FF5400]/10 flex items-center justify-center mb-6 group-hover:bg-[#FF5400]/20 transition-colors duration-300">
                  <svg className="w-8 h-8 text-[#FF5400]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 tracking-tight">Timely Delivery</h3>
                <p className="text-gray-600 leading-relaxed">
                  We respect your time and deadlines. Our streamlined processes ensure quality work delivered on schedule, every time.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="group p-8 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 rounded-full bg-[#0A2647]/10 flex items-center justify-center mb-6 group-hover:bg-[#0A2647]/20 transition-colors duration-300">
                  <svg className="w-8 h-8 text-[#0A2647]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 tracking-tight">Partnership Mindset</h3>
                <p className="text-gray-600 leading-relaxed">
                  We're not just vendors—we're partners in your success. We invest in understanding your business and growing with you.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
                className="group p-8 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 rounded-full bg-[#FF5400]/10 flex items-center justify-center mb-6 group-hover:bg-[#FF5400]/20 transition-colors duration-300">
                  <svg className="w-8 h-8 text-[#FF5400]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 tracking-tight">Measurable Results</h3>
                <p className="text-gray-600 leading-relaxed">
                  We believe in accountability. Our work is designed to deliver measurable outcomes that you can track and celebrate.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
                className="group p-8 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 rounded-full bg-[#0A2647]/10 flex items-center justify-center mb-6 group-hover:bg-[#0A2647]/20 transition-colors duration-300">
                  <svg className="w-8 h-8 text-[#0A2647]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"/>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 tracking-tight">Ongoing Support</h3>
                <p className="text-gray-600 leading-relaxed">
                  Our relationship doesn't end at project delivery. We provide ongoing support to ensure your continued success.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>



    </main>
  );
}